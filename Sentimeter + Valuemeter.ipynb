import pandas as pd
from dateutil.relativedelta import relativedelta
import numpy as np
from matplotlib import pyplot as plt
from matplotlib.pyplot import figure
# import warnings
# warnings.filterwarnings("ignore")
import math
from datetime import date, timedelta
import time
from tqdm import tqdm
from data import Data
d = Data()
from datetime import date, datetime
import pyodbc
import os
import sys
from indicators import LensMarketBreadthIndicators, LensValuationIndicators
from matplotlib.backends.backend_pdf import PdfPages


price_data = d.fetch_price_data(start_date='2006-01-01')

price_data['Symbol'] = price_data['Symbol'].astype(str).str.strip()
price_data = price_data[price_data['Date'] != '2022-03-07']

# Get the top 500 stocks for each date
top500 = price_data.groupby('Date').apply(lambda x: x.sort_values(by='Mcap', ascending=False).head(1000)).reset_index(drop=True)

# Filter the original DataFrame based on top 500 stocks
price_data = price_data[price_data['Symbol'].isin(top500['Symbol'].unique())].reset_index(drop=True)

# Create a 'Rank' column based on market capitalization within each date
price_data['Rank'] = price_data.groupby('Date')['Mcap'].rank(ascending=False, method='first')

price_data['CapSize'] = np.where((price_data['Rank'] <=100), 
                                   'Large', 
                                   np.where((price_data['Rank'] >=251), 
                                            'Small', 
                                            'Mid'))
price_data.tail()

Value_rawdata = d.fetch_data_from_database(table_name='Value_rawdata', start_date='2006-01-01')
SectorThemeGics = d.fetch_data_from_database(table_name='SectorThemeGics')

master_nifty_data = pd.read_excel(r'\\*************\c$\Users\Administrator\PycharmProjects\Projects\SectorStyle_Data_Pipeline\latest_outputs\Style NAV2.xlsx')
master_nifty_data = master_nifty_data[['Date', 'NIFTY', 'NIFTYMIDCAP150','NIFTYSMALLCAP25', 'NIFTY500', 'NFT500EQUAL']]
master_nifty_data.rename(columns={
                                'NIFTY':'Large',
                                'NIFTYMIDCAP150':'Mid',
                                'NIFTYSMALLCAP25':'Small',
                                'NIFTY500':'Nifty 500',
                                'NFT500EQUAL':'Nifty 500 Equal Wtd',
                                }, inplace=True)

master_nifty_data['Date'] = pd.to_datetime(master_nifty_data['Date'])
valid_trading_dates = d.fetch_stock_data('NIFTY', start_date='2006-01-01').Date
master_nifty_data = master_nifty_data[master_nifty_data['Date'].isin(valid_trading_dates)]
# master_nifty_data.tail(10)

mbi = LensMarketBreadthIndicators(price_data, master_nifty_data, smooth=10) 
dist_52w_high = mbi.distance_52w_high()
dist_alltime_high = mbi.distance_all_time_high()
beating_nifty_signal = mbi.beating_benchmark_signal()
nifty_eqwt_spreads = mbi.nifty_eqwt_spreads()
new_highs_12m = mbi.new_highs_12m()
highs_lows_52w = mbi.highs_lows_52w().filter(like='High')
breadth_alpha, breadth_absolute = mbi.breadth_alpha_absolute()


# concatenate all the above dataframes
all_mb = pd.concat([dist_52w_high, dist_alltime_high, beating_nifty_signal, nifty_eqwt_spreads, \
                    new_highs_12m, highs_lows_52w, breadth_alpha, breadth_absolute], axis=1, join='inner')

vali = LensValuationIndicators(price_data, Value_rawdata, smooth=10)
above_historical_valuation = vali.compute_stocks_above_historical_valuations()
valuation_premium_discount = vali.valuation_premium_discount()

all_val = pd.concat([above_historical_valuation, valuation_premium_discount], axis=1, join='inner')

sentimeter = (all_mb.rolling(252).rank(pct=True)).mean(axis=1).rank(pct=True)
valuemeter = (all_val.rolling(252).rank(pct=True)).mean(axis=1).rank(pct=True)

sentimeter.tail(252*5).plot(figsize=(12,4), grid=True, title=f'Sentimeter (total_indicators={all_mb.shape[1]})')

valuemeter.tail(252*5).plot(figsize=(12,4), grid=True, title=f'Valuemeter (total_indicators={all_val.shape[1]})')


# how to align two series
sentimeter = sentimeter.reindex(valuemeter.index, method='nearest')
valuemeter = valuemeter.reindex(sentimeter.index, method='nearest')

sentiment_value_combined_meter = sentimeter.sub(valuemeter).to_frame('Sentimeter-Valuemeter')

# sentiment_value_combined_meter.rank(pct=True).tail(252*2)\
#     .plot(figsize=(12,4), grid=True, title='Sentimeter-Valuemeter Reranked', legend=False)

plt.figure(figsize=(12,5))
plt.plot(sentiment_value_combined_meter.rank(pct=True).tail(252*5))
# 3 lines equally divides chart
plt.axhline(0.33, color='r', linestyle='--')
plt.axhline(0.67, color='g', linestyle='--')
plt.axhline(0.5, color='y', linewidth=1)
plt.title('Sentimeter-Valuemeter')
# plt.grid(True)
plt.tight_layout()
plt.show()

with PdfPages('./outputs/Sentimeter_Valuemeter_Combined.pdf') as pdf:
    lookback = 252*5 
    # Page 1: Rules
    # Add text content to the first page of the PDF
    fig, ax = plt.subplots(figsize=(12, 5))
    ax.axis('off')  # Remove axes
    
    # Title
    ax.text(0.5, 0.9, 'Sentimeter-Valuemeter', 
            ha='center', va='center', fontsize=16, fontweight='bold', transform=ax.transAxes)
    
    # Subtitle
    ax.text(0.5, 0.85, 'Market Sentiment and Valuation Indicators', 
            ha='center', va='center', fontsize=12, style='italic', transform=ax.transAxes)
    
    # Main content
    content = '''Overview:
• Sentimeter: Composite sentiment indicator based on market breadth signals 
• Valuemeter: Valuation indicator across market cap segments
• Senti-Value Meter: Combined sentiment and valuation signal

Market Beadth Indicators:
• Distance from 52W High
• Distance from All Time High
• Beating Benchmark (Nifty 500)
• Nifty500 vs Nifty500 Equal-Weight Spread
• New Highs 12M
• 52w Highs
• Breadth Alpha/Absolute

Valuation Indicators:
• % Stocks Above Rolling Historical 3-5 Y PE/PB
• PE/PB Premium/Discount vs Rolling 3-5Y Mean/Median

Reference Lines:
• Red Line (33%%): Risk-OFF Zone
• Green Line (67%%): Risk-ON Zone
• Yellow Line (50%%): Neutral zone

Data Period: Last 5 years
Smootheneing: 10-day rolling
'''
    
    ax.text(0.1, 0.65, content, ha='left', va='top', fontsize=10, 
            transform=ax.transAxes, linespacing=1.2)
    
    # Footer
    from datetime import datetime
    current_date = datetime.now().strftime('%%B %%d, %%Y')
    ax.text(0.5, 0.05, f'Generated on: {current_date}', 
            ha='center', va='center', fontsize=10, style='italic', transform=ax.transAxes)
    
    pdf.savefig()
    plt.close()
    

    # Page 2: Senti-Value meter only
    plt.figure(figsize=(12,5))
    plt.plot(sentiment_value_combined_meter.rank(pct=True).tail(lookback), label='Senti-Value meter', color='blue')
    plt.axhline(0.33, color='r', linestyle='--')
    plt.axhline(0.67, color='g', linestyle='--')
    plt.axhline(0.5, color='y', linewidth=1)
    plt.title('Senti-Value Meter')
    plt.legend()
    plt.tight_layout()
    pdf.savefig()
    plt.close()
    
    # Page 3: Sentimeter only
    plt.figure(figsize=(12,5))
    plt.plot(sentimeter.tail(lookback), label='Sentimeter', color='blue')
    plt.axhline(0.33, color='r', linestyle='--')
    plt.axhline(0.67, color='g', linestyle='--')
    plt.axhline(0.5, color='y', linewidth=1)
    plt.title('Sentimeter')
    plt.legend()
    plt.tight_layout()
    pdf.savefig()
    plt.close()
    
    # Page 4: Valuemeter only
    plt.figure(figsize=(12,5))
    plt.plot(valuemeter.tail(lookback), label='Valuemeter', color='b')
    plt.axhline(0.33, color='r', linestyle='--')
    plt.axhline(0.67, color='g', linestyle='--')
    plt.axhline(0.5, color='y', linewidth=1)
    plt.title('Valuemeter')
    plt.legend()
    plt.tight_layout()
    pdf.savefig()
    plt.close()


