import pandas as pd
from dateutil.relativedelta import relativedelta
import numpy as np
from matplotlib import pyplot as plt
from matplotlib.pyplot import figure
# import warnings
# warnings.filterwarnings("ignore")
import math
from datetime import date, timedelta
import time
from tqdm import tqdm
from data import Data
d = Data()
from datetime import date, datetime
import pyodbc
import os
import sys
from indicators import LensMarketBreadthIndicators, LensValuationIndicators
from matplotlib.backends.backend_pdf import PdfPages


price_data = d.fetch_price_data(start_date='2006-01-01')

price_data['Symbol'] = price_data['Symbol'].astype(str).str.strip()
price_data = price_data[price_data['Date'] != '2022-03-07']

# Get the top 500 stocks for each date
top500 = price_data.groupby('Date').apply(lambda x: x.sort_values(by='Mcap', ascending=False).head(1000)).reset_index(drop=True)

# Filter the original DataFrame based on top 500 stocks
price_data = price_data[price_data['Symbol'].isin(top500['Symbol'].unique())].reset_index(drop=True)

# Create a 'Rank' column based on market capitalization within each date
price_data['Rank'] = price_data.groupby('Date')['Mcap'].rank(ascending=False, method='first')

price_data['CapSize'] = np.where((price_data['Rank'] <=100), 
                                   'Large', 
                                   np.where((price_data['Rank'] >=251), 
                                            'Small', 
                                            'Mid'))
price_data.tail()

Value_rawdata = d.fetch_data_from_database(table_name='Value_rawdata', start_date='2006-01-01')
SectorThemeGics = d.fetch_data_from_database(table_name='SectorThemeGics')

master_nifty_data = pd.read_excel(r'\\*************\c$\Users\Administrator\PycharmProjects\Projects\SectorStyle_Data_Pipeline\latest_outputs\Style NAV2.xlsx')
master_nifty_data = master_nifty_data[['Date', 'NIFTY', 'NIFTYMIDCAP150','NIFTYSMALLCAP25', 'NIFTY500', 'NFT500EQUAL']]
master_nifty_data.rename(columns={
                                'NIFTY':'Large',
                                'NIFTYMIDCAP150':'Mid',
                                'NIFTYSMALLCAP25':'Small',
                                'NIFTY500':'Nifty 500',
                                'NFT500EQUAL':'Nifty 500 Equal Wtd',
                                }, inplace=True)

master_nifty_data['Date'] = pd.to_datetime(master_nifty_data['Date'])
valid_trading_dates = d.fetch_stock_data('NIFTY', start_date='2006-01-01').Date
master_nifty_data = master_nifty_data[master_nifty_data['Date'].isin(valid_trading_dates)]
# master_nifty_data.tail(10)

mbi = LensMarketBreadthIndicators(price_data, master_nifty_data, smooth=10) 
dist_52w_high = mbi.distance_52w_high()
dist_alltime_high = mbi.distance_all_time_high()
beating_nifty_signal = mbi.beating_benchmark_signal()
nifty_eqwt_spreads = mbi.nifty_eqwt_spreads()
new_highs_12m = mbi.new_highs_12m()
highs_lows_52w = mbi.highs_lows_52w().filter(like='High')
breadth_alpha, breadth_absolute = mbi.breadth_alpha_absolute()


# concatenate all the above dataframes
all_mb = pd.concat([dist_52w_high, dist_alltime_high, beating_nifty_signal, nifty_eqwt_spreads, \
                    new_highs_12m, highs_lows_52w, breadth_alpha, breadth_absolute], axis=1, join='inner')

vali = LensValuationIndicators(price_data, Value_rawdata, smooth=10)
above_historical_valuation = vali.compute_stocks_above_historical_valuations()
valuation_premium_discount = vali.valuation_premium_discount()

all_val = pd.concat([above_historical_valuation, valuation_premium_discount], axis=1, join='inner')

sentimeter = (all_mb.rolling(252).rank(pct=True)).mean(axis=1).rank(pct=True)
valuemeter = (all_val.rolling(252).rank(pct=True)).mean(axis=1).rank(pct=True)

sentimeter.tail(252*5).plot(figsize=(12,4), grid=True, title=f'Sentimeter (total_indicators={all_mb.shape[1]})')

valuemeter.tail(252*5).plot(figsize=(12,4), grid=True, title=f'Valuemeter (total_indicators={all_val.shape[1]})')


# how to align two series
sentimeter = sentimeter.reindex(valuemeter.index, method='nearest')
valuemeter = valuemeter.reindex(sentimeter.index, method='nearest')

sentiment_value_combined_meter = sentimeter.sub(valuemeter).to_frame('Sentimeter-Valuemeter')

# sentiment_value_combined_meter.rank(pct=True).tail(252*2)\
#     .plot(figsize=(12,4), grid=True, title='Sentimeter-Valuemeter Reranked', legend=False)

plt.figure(figsize=(12,5))
plt.plot(sentiment_value_combined_meter.rank(pct=True).tail(252*5))
# 3 lines equally divides chart
plt.axhline(0.33, color='r', linestyle='--')
plt.axhline(0.67, color='g', linestyle='--')
plt.axhline(0.5, color='y', linewidth=1)
plt.title('Sentimeter-Valuemeter')
# plt.grid(True)
plt.tight_layout()
plt.show()

# PDF for plotting all charts
# I also want to plot all the charts in one pdf on different pages
lookback = 252*5
with PdfPages('Sentimeter_Valuemeter.pdf') as pdf:
    plt.figure(figsize=(12,5))
    plt.plot(sentiment_value_combined_meter.rank(pct=True).tail(lookback), label='Senti-Value meter')
    plt.plot(sentimeter.tail(lookback), label='Sentimeter')
    plt.plot(valuemeter.tail(lookback), label='Valuemeter')
    plt.axhline(0.33, color='r', linestyle='--')
    plt.axhline(0.67, color='g', linestyle='--')
    plt.axhline(0.5, color='y', linewidth=1)
    plt.title('Sentimeter-Valuemeter')
    plt.legend()
    plt.tight_layout()
    # pdf.savefig()
    # plt.close()
    plt.show()

