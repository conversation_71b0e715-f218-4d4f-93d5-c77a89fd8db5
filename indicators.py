import pandas as pd
from dateutil.relativedelta import relativedelta
import numpy as np
from matplotlib import pyplot as plt
from matplotlib.pyplot import figure
import warnings
warnings.filterwarnings("ignore")
import math
from datetime import date, timedelta
import time
from tqdm import tqdm
from data import Data
d = Data()
from datetime import date, datetime
import pyodbc
import os
import sys
from scipy.stats.mstats import winsorize as wn


class LensMarketBreadthIndicators:
    def __init__(self, 
                 price_data, 
                 nifty_data, 
                 smooth=5, 
                 top_n=500, 
                 min_periods=10,
                 default_benchmark='Nifty 500'):
        """
        price_data: DataFrame with at least ['Date','Symbol','Close','Mcap']
        nifty_data: DataFrame with at least ['Date','NIFTY','NIFTYMIDCAP150','NIFTYSMALLCAP250','NIFTY500','NFT500EQUAL', ...]
        smooth: default rolling window for all signals
        top_n: default top N by market cap to include
        default_benchmark: Default index column to use for most signals (e.g., 'NIFTY500')
        """
        self.price_data = price_data.copy()
        self.nifty_data = nifty_data.copy()
        self.smooth = smooth
        self.top_n = top_n
        self.min_periods = min_periods
        self.default_benchmark = default_benchmark

    @staticmethod
    def _top_n_by_mcap(df, n):
        return df.sort_values(['Date', 'Mcap'], ascending=[True, False]).groupby('Date', group_keys=False).head(n)

    # 1. Distance from all time high (top_n stocks by Mcap)
    def distance_all_time_high(self, smooth=None, top_n=None):
        smooth = smooth or self.smooth
        top_n = top_n or self.top_n
        df = self.price_data.copy()
        df['Alltime_high'] = df.groupby('Symbol')['Close'].transform(lambda x: x.expanding(min_periods=self.min_periods).max())
        df['Dist_high'] = ((df['Close'] / df['Alltime_high']) - 1) * 100
        df = self._top_n_by_mcap(df, n=top_n)
        out = df.pivot_table(index='Date', values='Dist_high', aggfunc='mean').rename(columns={'Dist_high': '%Distance from All Time High'})
        return out.rolling(smooth).mean()

    # 2. Distance from 52W high (top_n by Mcap)
    def distance_52w_high(self, window=252, smooth=None, top_n=None):
        smooth = smooth or self.smooth
        top_n = top_n or self.top_n
        df = self.price_data.copy()
        df['52W_High'] = df.groupby('Symbol')['Close'].transform(lambda x: x.rolling(window=window, min_periods=self.min_periods).max())
        df['Dist_high'] = ((df['Close'] / df['52W_High']) - 1) * 100
        df = self._top_n_by_mcap(df, n=top_n)
        out = df.pivot_table(index='Date', values='Dist_high', aggfunc='mean').rename(columns={'Dist_high': '%Distance from 52W High'})
        return out.rolling(smooth).mean()

    # 3. Beating Benchmark (Default: NIFTY500)
    def beating_benchmark_signal(self, p_months=12, smooth=None, top_n=None, benchmark=None):
        smooth = smooth or self.smooth
        top_n = top_n or self.top_n
        benchmark = benchmark or self.default_benchmark
        # print(benchmark)
        price = self.price_data.copy()
        bench = self.nifty_data[['Date', benchmark]].copy().rename(columns={benchmark:'Close'})
        price['Date'] = pd.to_datetime(price['Date'])
        bench['Date'] = pd.to_datetime(bench['Date'])
        bench[f'{p_months}M_Rolling_Return'] = bench['Close'].pct_change(p_months*21)
        price[f'{p_months}M_Rolling_Return'] = price.groupby('Symbol')['Close'].pct_change(p_months*21)
        merged = pd.merge(price, bench[['Date', f'{p_months}M_Rolling_Return']], on='Date', suffixes=('', '_BENCH'))
        merged['Beats_BENCH'] = (merged[f'{p_months}M_Rolling_Return'] > merged[f'{p_months}M_Rolling_Return_BENCH']).astype(int)
        merged = self._top_n_by_mcap(merged, n=top_n)
        daily_pct = merged.groupby('Date')['Beats_BENCH'].mean() * 100
        return daily_pct.rename(f'%Stocks Beating {benchmark} on {p_months}M Rolling').rolling(smooth).mean().to_frame()

    # 4. Nifty500 vs Nifty500 Equal-Weight Spread
    def nifty_eqwt_spreads(self, window=252, smooth=None):
        smooth = smooth or self.smooth
        df = self.nifty_data.copy()
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.set_index('Date').sort_index()
        df['CW_1y'] = df['Nifty 500'].pct_change(window)*100
        df['EW_1y'] = df['Nifty 500 Equal Wtd'].pct_change(window)*100
        df['N500:Eq Wtd-Cap Wtd Spread'] = df['EW_1y'] - df['CW_1y']
        spread = df[['N500:Eq Wtd-Cap Wtd Spread']].rolling(smooth).mean()
        return spread

    # 5. New Highs 12M - Only major indices (NIFTY, MIDCAP150, SMALLCAP250, NIFTY500)
    def new_highs_12m(self, window=252, smooth=None):
        smooth = smooth or self.smooth
        # Consider only these index columns
        major_cols = ['Large', 'Mid', 'Small', 'Nifty 500']
        df = self.nifty_data[['Date'] + [col for col in major_cols if col in self.nifty_data.columns]].copy()
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.set_index('Date')
        result = pd.DataFrame(index=df.index)
        for col in df.columns:
            result[f'{col}_Rolling_High'] = df[col].rolling(window=window, min_periods=self.min_periods).max()
            result[f'12M_%_New_High_{col}'] = (df[col] == result[f'{col}_Rolling_High']).astype(int).rolling(window, min_periods=self.min_periods).mean() * 100
        # Only the "% New High" columns, and apply smoothing
        out = result.filter(like='12M_%_New_High').rolling(smooth).mean()
        return out

    # 6. 52w highs/lows (stock universe, but flexible)
    def highs_lows_52w(self, near_thresh=0.10, smooth=None, top_n=None):
        smooth = smooth or self.smooth
        top_n = top_n or self.top_n
        df = self.price_data.copy()
        df = df.sort_values(['Symbol', 'Date'])
        df['52WHigh'] = df.groupby('Symbol')['Close'].rolling(window=252, min_periods=self.min_periods).max().reset_index(level=0, drop=True)
        df['52WLow'] = df.groupby('Symbol')['Close'].rolling(window=252, min_periods=self.min_periods).min().reset_index(level=0, drop=True)
        df['%Stocks Making New 52W High'] = np.where(df['Close'] == df['52WHigh'], 1, 0)
        df['%Stocks Making New 52W Low'] = np.where(df['Close'] == df['52WLow'], 1, 0)
        df['%Stocks Near 52W High'] = np.where(df['Close'] >= (1-near_thresh)*df['52WHigh'], 1, 0)
        df['%Stocks Near 52W Low']  = np.where(df['Close'] <= (1+near_thresh)*df['52WLow'], 1, 0)
        df = self._top_n_by_mcap(df, n=top_n)
        metrics = (df.groupby('Date')
                     [['%Stocks Making New 52W High','%Stocks Near 52W High','%Stocks Making New 52W Low','%Stocks Near 52W Low']]
                     .mean()*100)
        return metrics.rolling(smooth).mean()

    # 7. Breadth alpha/absolute - using default benchmark NIFTY500
    def breadth_alpha_absolute(self, rolling_periods=[1,3,6,12], smooth=None, top_n=None, benchmark=None):
        smooth = smooth or self.smooth
        top_n = top_n or self.top_n
        benchmark = benchmark or self.default_benchmark
        price = self.price_data.copy()
        bench = self.nifty_data[['Date', benchmark]].copy().rename(columns={benchmark:'Close'})
        out_alpha, out_abs = {}, {}
        for p in rolling_periods:
            bench[f'{p}M_Rolling_Return'] = bench['Close'].pct_change(21*p)
            price[f'{p}M_Rolling_Return'] = price.groupby('Symbol')['Close'].pct_change(21*p)
            merged = pd.merge(price, bench[['Date',f'{p}M_Rolling_Return']], on='Date', suffixes=('', '_BENCH'))
            merged[f'Market Breadth-Alpha_{p}M'] = (merged[f'{p}M_Rolling_Return'] > merged[f'{p}M_Rolling_Return_BENCH']).astype(int)
            merged[f'Market Breadth Abs_{p}M']   = (merged[f'{p}M_Rolling_Return'] > 0).astype(int)
            merged = self._top_n_by_mcap(merged, n=top_n)
            out_alpha[p] = merged.groupby('Date')[f'Market Breadth-Alpha_{p}M'].mean().rolling(smooth).mean()*100
            out_abs[p]   = merged.groupby('Date')[f'Market Breadth Abs_{p}M'].mean().rolling(smooth).mean()*100
        x, y = pd.concat(out_alpha.values(), axis=1), pd.concat(out_abs.values(), axis=1)
        return x,y
    
    # 8. Absolute positive/negative stocks
    def calculate_absPosNegStocks(self, window=None, top_n=None):
        df = self.price_data.copy()
        window = window or self.trading_days
        top_n = top_n or self.top_n
        df['pctChange'] = df.groupby('Symbol')['Close'].pct_change(window)
        df['abs_Positive'] = (df['pctChange'] > 0).astype(int)
        df['abs_Negative'] = (df['pctChange'] < 0).astype(int)
        df = self._top_n_by_mcap(df, n=top_n)
        x = df.groupby('Date').agg(
            pct_stocks_positive=('abs_Positive', 'mean'),
            pct_stocks_negative=('abs_Negative', 'mean')) * 100
        x.columns = ['%Stocks absolute positive over 1Y', '%Stocks absolute negative over 1Y']
        return x

# ===========================================================================================
# ===========================================================================================

class LensValuationIndicators:
    def __init__(
        self, price_data, fundamentals,
        metrics=('PE', 'PB'),
        years=(3, 5),
        methods=('mean', 'median'),
        cap_sizes=('Large', 'Mid', 'Small'),
        trading_days=252,
        smooth=5,
        # min_periods=10,
    ):
        """
        price_data: DataFrame with ['Date', 'Symbol', 'Close', 'Mcap']
        fundamentals: DataFrame with ['Date', 'Symbol', 'PE', 'PB', ...]
        """
        self.price_data = price_data.copy()
        self.fundamentals = fundamentals.copy()
        self.metrics = metrics
        self.years = years
        self.methods = methods
        self.cap_sizes = cap_sizes
        self.trading_days = trading_days
        self.smooth = smooth
        # self.min_periods = min_periods

    @staticmethod
    def _winsorize_agg(ser):
        arr = ser.dropna().values
        return np.nan if len(arr) == 0 else wn(arr, limits=[0.1, 0.1]).mean()

    @staticmethod
    def _top_cap_ranks(df, top_n=500):
        # Assign rank and cap size; then take top 500 by Mcap per Date
        df = df.copy()
        df['Rank'] = df.groupby('Date')['Mcap'].rank(ascending=False, method='first')
        df['CapSize'] = np.where(df['Rank'] <= 100, 'Large',
                         np.where(df['Rank'] >= 251, 'Small', 'Mid'))
        df = df.sort_values(['Date', 'Mcap'], ascending=[True, False]).groupby('Date', group_keys=False).head(top_n)
        return df

    def compute_stocks_above_historical_valuations(self):
        """% stocks above historical rolling PE/PB, for all windows/methods/cap sizes."""
        df = self._top_cap_ranks(self.price_data)
        df = pd.merge(df, self.fundamentals[['Date', 'Symbol'] + list(self.metrics)],
                      on=['Date', 'Symbol'], how='left').dropna(subset=self.metrics)
        outs = []
        for metric in self.metrics:
            for year in self.years:
                roll_len = self.trading_days * year
                for method in self.methods:
                    label = 'Avg' if method == 'mean' else 'Median'
                    grouped = df.groupby(['Date', 'CapSize'])[metric].agg(self._winsorize_agg).unstack()
                    rolling = grouped.rolling(roll_len, min_periods=1).agg(method)
                    key = f'%Stocks Above {year}Y {label} {metric}'
                    for cap in self.cap_sizes:
                        if cap not in rolling.columns:
                            continue
                        idx = (df['CapSize'] == cap)
                        rolling_map = rolling[cap].reindex(df.loc[idx, 'Date']).values
                        above = (df.loc[idx, metric] >= rolling_map).astype(float)
                        out = (
                            df.loc[idx, ['Date']]
                            .assign(Above=above.values)
                            .groupby('Date')['Above'].mean()
                            .to_frame(f'{key}_{cap}cap')
                            .mul(100).round(2)
                        )
                        outs.append(out)
        final = pd.concat(outs, axis=1).sort_index()
        if self.smooth:
            final = final.rolling(self.smooth, min_periods=1).mean()
        return final


    def valuation_premium_discount(self, rolling_window_days=None):
        """
        PE/PB premium/discount vs rolling mean by CapSize, plus rolling mean/median for 3Y and 5Y windows.
        """
        # Set default rolling window days if not provided
        rolling_window_days = rolling_window_days or self.trading_days

        # Top 500 by cap and merge
        df = self._top_cap_ranks(self.price_data)
        a = pd.merge(df, self.fundamentals, on=['Symbol', 'Date'], how='left')
        b = a.pivot_table(index='Date', columns='CapSize', values=list(self.metrics), aggfunc='mean')
        b.columns = [f"{col[0]}_{col[1]}cap" for col in b.columns]

        # Calculate rolling window mean for each cap/metric and each year
        for col in b.columns:
            for y in self.years:
                # Calculate window size for this specific year
                window_days = rolling_window_days * y
                b[f"Avg {y}Y {col}"] = b[col].rolling(window_days, min_periods=1).mean()
                b[f"{y}Y Premium Discount Ratio - {col}"] = (b[col] / b[f"Avg {y}Y {col}"] - 1).mul(100)

        c = b.filter(like='Premium Discount Ratio')
        if self.smooth:
            c = c.rolling(self.smooth, min_periods=1).mean()
        return c.dropna().sort_index()

